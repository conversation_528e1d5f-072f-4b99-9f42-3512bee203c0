{"project_root": "/home/<USER>/diff_analyzer/test_project", "statistics": {"total_files": 13, "total_dependencies": 0, "entry_points": 13, "leaf_nodes": 13, "circular_dependencies": 0, "external_packages": 3, "avg_dependencies_per_file": 0.0, "avg_dependents_per_file": 0.0, "max_dependencies": 0, "max_dependents": 0, "file_types": {".ts": 6, ".vue": 7}, "most_imported_files": [["/home/<USER>/diff_analyzer/test_project/src/main.ts", 0], ["/home/<USER>/diff_analyzer/test_project/src/App.vue", 0], ["/home/<USER>/diff_analyzer/test_project/src/store/index.ts", 0], ["/home/<USER>/diff_analyzer/test_project/src/components/SocialLinks.vue", 0], ["/home/<USER>/diff_analyzer/test_project/src/components/ContactInfo.vue", 0]], "most_dependent_files": [["/home/<USER>/diff_analyzer/test_project/src/main.ts", 0], ["/home/<USER>/diff_analyzer/test_project/src/App.vue", 0], ["/home/<USER>/diff_analyzer/test_project/src/store/index.ts", 0], ["/home/<USER>/diff_analyzer/test_project/src/components/SocialLinks.vue", 0], ["/home/<USER>/diff_analyzer/test_project/src/components/ContactInfo.vue", 0]]}, "files": {"src/main.ts": {"path": "src/main.ts", "is_entry_point": true, "is_leaf_node": false, "dependencies": [], "dependents": [], "imports": [{"module": "vue", "type": "named", "dependency_type": "external", "imported_names": ["createApp"], "line_number": 1, "is_type_only": false}, {"module": "src/App.vue", "type": "default", "dependency_type": "local", "imported_names": ["App"], "line_number": 2, "is_type_only": false}, {"module": "src/router", "type": "default", "dependency_type": "local", "imported_names": ["router"], "line_number": 3, "is_type_only": false}, {"module": "src/store", "type": "named", "dependency_type": "local", "imported_names": ["store"], "line_number": 4, "is_type_only": false}], "exports": []}, "src/App.vue": {"path": "src/App.vue", "is_entry_point": true, "is_leaf_node": false, "dependencies": [], "dependents": [], "imports": [{"module": "vue", "type": "named", "dependency_type": "external", "imported_names": ["defineComponent"], "line_number": 1, "is_type_only": false}, {"module": "src/components/UserProfile.vue", "type": "default", "dependency_type": "local", "imported_names": ["UserProfile"], "line_number": 2, "is_type_only": false}, {"module": "src/store", "type": "named", "dependency_type": "local", "imported_names": ["useStore"], "line_number": 3, "is_type_only": false}, {"module": "src/components/UserProfile.vue", "type": "default", "dependency_type": "local", "imported_names": ["UserProfile"], "line_number": 2, "is_type_only": false}], "exports": ["export default defineComponent({\n  name: 'App',\n  components: {\n    UserProfile\n  },\n  setup() {\n    const store = useStore()\n    \n    return {\n      currentUser: store.state.user\n    }\n  }\n})"]}, "src/store/index.ts": {"path": "src/store/index.ts", "is_entry_point": true, "is_leaf_node": false, "dependencies": [], "dependents": [], "imports": [{"module": "vuex", "type": "named", "dependency_type": "external", "imported_names": ["createStore"], "line_number": 1, "is_type_only": false}, {"module": "src/types/User", "type": "named", "dependency_type": "local", "imported_names": ["User"], "line_number": 2, "is_type_only": false}, {"module": "src/utils/userService", "type": "named", "dependency_type": "local", "imported_names": ["userService"], "line_number": 3, "is_type_only": false}], "exports": ["export const store = createStore<State>({\n  state: {\n    user: null,\n    loading: false\n  },\n  mutations: {\n    SET_USER(state, user: User) {\n      state.user = user\n    },\n    SET_LOADING(state, loading: boolean) {\n      state.loading = loading\n    }\n  },\n  actions: {\n    async fetchUser({ commit }, userId: number) {\n      commit('SET_LOADING', true)\n      try {\n        const user = await userService.getUser(userId)\n        commit('SET_USER', user)\n      } catch (error) {\n        console.error('Failed to fetch user:', error)\n      } finally {\n        commit('SET_LOADING', false)\n      }\n    }\n  }\n})", "export function useStore() {\n  return store\n}"]}, "src/components/SocialLinks.vue": {"path": "src/components/SocialLinks.vue", "is_entry_point": false, "is_leaf_node": false, "dependencies": [], "dependents": [], "imports": [{"module": "vue", "type": "named", "dependency_type": "external", "imported_names": ["defineComponent", "PropType"], "line_number": 1, "is_type_only": false}, {"module": "src/types/User", "type": "named", "dependency_type": "local", "imported_names": ["SocialLink"], "line_number": 2, "is_type_only": false}], "exports": ["export default defineComponent({\n  name: 'SocialLinks',\n  props: {\n    links: {\n      type: Array as PropType<SocialLink[]>,\n      default: () => []\n    }\n  }\n})"]}, "src/components/ContactInfo.vue": {"path": "src/components/ContactInfo.vue", "is_entry_point": false, "is_leaf_node": false, "dependencies": [], "dependents": [], "imports": [{"module": "vue", "type": "named", "dependency_type": "external", "imported_names": ["defineComponent", "PropType"], "line_number": 1, "is_type_only": false}, {"module": "src/components/SocialLinks.vue", "type": "default", "dependency_type": "local", "imported_names": ["SocialLinks"], "line_number": 2, "is_type_only": false}, {"module": "src/types/User", "type": "named", "dependency_type": "local", "imported_names": ["Contact"], "line_number": 3, "is_type_only": false}, {"module": "src/components/SocialLinks.vue", "type": "default", "dependency_type": "local", "imported_names": ["SocialLinks"], "line_number": 2, "is_type_only": false}], "exports": ["export default defineComponent({\n  name: 'ContactInfo',\n  components: {\n    SocialLinks\n  },\n  props: {\n    contact: {\n      type: Object as PropType<Contact>,\n      required: true\n    }\n  }\n})"]}, "src/components/UserAvatar.vue": {"path": "src/components/UserAvatar.vue", "is_entry_point": false, "is_leaf_node": false, "dependencies": [], "dependents": [], "imports": [{"module": "vue", "type": "named", "dependency_type": "external", "imported_names": ["defineComponent"], "line_number": 1, "is_type_only": false}], "exports": ["export default defineComponent({\n  name: 'UserAvatar',\n  props: {\n    src: {\n      type: String,\n      required: true\n    },\n    alt: {\n      type: String,\n      default: 'User Avatar'\n    },\n    size: {\n      type: Number,\n      default: 48\n    }\n  }\n})"]}, "src/components/UserProfile.vue": {"path": "src/components/UserProfile.vue", "is_entry_point": false, "is_leaf_node": false, "dependencies": [], "dependents": [], "imports": [{"module": "vue", "type": "named", "dependency_type": "external", "imported_names": ["defineComponent", "PropType"], "line_number": 1, "is_type_only": false}, {"module": "src/components/UserAvatar.vue", "type": "default", "dependency_type": "local", "imported_names": ["UserAvatar"], "line_number": 2, "is_type_only": false}, {"module": "src/components/ContactInfo.vue", "type": "default", "dependency_type": "local", "imported_names": ["ContactInfo"], "line_number": 3, "is_type_only": false}, {"module": "src/types/User", "type": "named", "dependency_type": "local", "imported_names": ["User"], "line_number": 4, "is_type_only": false}, {"module": "src/components/UserAvatar.vue", "type": "default", "dependency_type": "local", "imported_names": ["UserAvatar"], "line_number": 2, "is_type_only": false}, {"module": "src/components/ContactInfo.vue", "type": "default", "dependency_type": "local", "imported_names": ["ContactInfo"], "line_number": 3, "is_type_only": false}], "exports": ["export default defineComponent({\n  name: 'UserProfile',\n  components: {\n    UserAvatar,\n    ContactInfo\n  },\n  props: {\n    user: {\n      type: Object as PropType<User>,\n      required: true\n    }\n  }\n})"]}, "src/utils/userService.ts": {"path": "src/utils/userService.ts", "is_entry_point": false, "is_leaf_node": false, "dependencies": [], "dependents": [], "imports": [{"module": "src/types/User", "type": "named", "dependency_type": "local", "imported_names": ["User"], "line_number": 1, "is_type_only": false}, {"module": "src/utils/apiClient", "type": "named", "dependency_type": "local", "imported_names": ["apiClient"], "line_number": 2, "is_type_only": false}], "exports": ["export const userService = {\n  async getUser(id: number): Promise<User> {\n    const response = await apiClient.get(`/users/${id}`)\n    return response.data\n  },\n\n  async updateUser(id: number, userData: Partial<User>): Promise<User> {\n    const response = await apiClient.put(`/users/${id}`, userData)\n    return response.data\n  },\n\n  async deleteUser(id: number): Promise<void> {\n    await apiClient.delete(`/users/${id}`)\n  }\n}"]}, "src/utils/apiClient.ts": {"path": "src/utils/apiClient.ts", "is_entry_point": false, "is_leaf_node": true, "dependencies": [], "dependents": [], "imports": [], "exports": ["export const apiClient = new ApiClient()"]}, "src/router/index.ts": {"path": "src/router/index.ts", "is_entry_point": true, "is_leaf_node": false, "dependencies": [], "dependents": [], "imports": [{"module": "vue-router", "type": "named", "dependency_type": "external", "imported_names": ["createRouter", "createWebHistory"], "line_number": 1, "is_type_only": false}, {"module": "src/views/Home.vue", "type": "default", "dependency_type": "local", "imported_names": ["Home"], "line_number": 2, "is_type_only": false}, {"module": "src/views/About.vue", "type": "dynamic", "dependency_type": "local", "imported_names": [], "line_number": 13, "is_type_only": false}], "exports": ["export default router"]}, "src/views/About.vue": {"path": "src/views/About.vue", "is_entry_point": false, "is_leaf_node": false, "dependencies": [], "dependents": [], "imports": [{"module": "vue", "type": "named", "dependency_type": "external", "imported_names": ["defineComponent"], "line_number": 1, "is_type_only": false}, {"module": "src/components/ContactInfo.vue", "type": "default", "dependency_type": "local", "imported_names": ["ContactInfo"], "line_number": 2, "is_type_only": false}, {"module": "src/types/User", "type": "named", "dependency_type": "local", "imported_names": ["Contact"], "line_number": 3, "is_type_only": false}, {"module": "src/components/ContactInfo.vue", "type": "default", "dependency_type": "local", "imported_names": ["ContactInfo"], "line_number": 2, "is_type_only": false}], "exports": ["export default defineComponent({\n  name: 'About',\n  components: {\n    ContactInfo\n  },\n  setup() {\n    const appContact: Contact = {\n      phone: '******-0123',\n      address: '123 Main St, Anytown, USA',\n      social: [\n        { platform: 'GitHub', url: 'https://github.com/example' },\n        { platform: 'Twitter', url: 'https://twitter.com/example' }\n      ]\n    }\n\n    return {\n      appContact\n    }\n  }\n})"]}, "src/views/Home.vue": {"path": "src/views/Home.vue", "is_entry_point": false, "is_leaf_node": false, "dependencies": [], "dependents": [], "imports": [{"module": "vue", "type": "named", "dependency_type": "external", "imported_names": ["defineComponent", "ref"], "line_number": 1, "is_type_only": false}, {"module": "src/components/UserProfile.vue", "type": "default", "dependency_type": "local", "imported_names": ["UserProfile"], "line_number": 2, "is_type_only": false}, {"module": "src/types/User", "type": "named", "dependency_type": "local", "imported_names": ["User"], "line_number": 3, "is_type_only": false}, {"module": "src/utils/userService", "type": "named", "dependency_type": "local", "imported_names": ["userService"], "line_number": 4, "is_type_only": false}, {"module": "src/components/UserProfile.vue", "type": "default", "dependency_type": "local", "imported_names": ["UserProfile"], "line_number": 2, "is_type_only": false}], "exports": ["export default defineComponent({\n  name: 'Home',\n  components: {\n    UserProfile\n  },\n  setup() {\n    const user = ref<User | null>(null)\n\n    const loadUser = async () => {\n      try {\n        user.value = await userService.getUser(1)\n      } catch (error) {\n        console.error('Failed to load user:', error)\n      }\n    }\n\n    return {\n      user,\n      loadUser\n    }\n  }\n})"]}, "src/types/User.ts": {"path": "src/types/User.ts", "is_entry_point": false, "is_leaf_node": true, "dependencies": [], "dependents": [], "imports": [], "exports": ["export interface SocialLink {\n  platform: string\n  url: string\n}", "export interface Contact {\n  phone?: string\n  address?: string\n  social: SocialLink[]\n}", "export interface User {\n  id: number\n  name: string\n  email: string\n  avatar: string\n  contact: Contact\n}"]}}, "external_dependencies": {"vue": ["/home/<USER>/diff_analyzer/test_project/src/main.ts", "/home/<USER>/diff_analyzer/test_project/src/App.vue", "/home/<USER>/diff_analyzer/test_project/src/components/SocialLinks.vue", "/home/<USER>/diff_analyzer/test_project/src/components/ContactInfo.vue", "/home/<USER>/diff_analyzer/test_project/src/components/UserAvatar.vue", "/home/<USER>/diff_analyzer/test_project/src/components/UserProfile.vue", "/home/<USER>/diff_analyzer/test_project/src/views/About.vue", "/home/<USER>/diff_analyzer/test_project/src/views/Home.vue"], "vuex": ["/home/<USER>/diff_analyzer/test_project/src/store/index.ts"], "vue-router": ["/home/<USER>/diff_analyzer/test_project/src/router/index.ts"]}, "circular_dependencies": [], "entry_points": ["src/main.ts", "src/App.vue", "src/store/index.ts", "src/components/SocialLinks.vue", "src/components/ContactInfo.vue", "src/components/UserAvatar.vue", "src/components/UserProfile.vue", "src/utils/userService.ts", "src/utils/apiClient.ts", "src/router/index.ts", "src/views/About.vue", "src/views/Home.vue", "src/types/User.ts"], "leaf_nodes": ["src/main.ts", "src/App.vue", "src/store/index.ts", "src/components/SocialLinks.vue", "src/components/ContactInfo.vue", "src/components/UserAvatar.vue", "src/components/UserProfile.vue", "src/utils/userService.ts", "src/utils/apiClient.ts", "src/router/index.ts", "src/views/About.vue", "src/views/Home.vue", "src/types/User.ts"]}