"""
Dependency Analyzer for Vue.js and React Projects

This module provides tools to parse Vue.js and React project source code
and extract dependency relationships using tree-sitter-typescript.
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, List, Set, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from enum import Enum

import tree_sitter
from tree_sitter import Language, Parser
import tree_sitter_typescript as ts_typescript


class ImportType(Enum):
    """Types of imports that can be found in source code."""
    DEFAULT = "default"
    NAMED = "named"
    NAMESPACE = "namespace"
    DYNAMIC = "dynamic"
    REQUIRE = "require"


class DependencyType(Enum):
    """Types of dependencies based on their location."""
    LOCAL = "local"          # Relative imports within the project
    EXTERNAL = "external"    # node_modules dependencies
    BUILTIN = "builtin"      # Built-in modules


@dataclass
class ImportInfo:
    """Information about a single import statement."""
    source_file: str
    imported_module: str
    import_type: ImportType
    dependency_type: DependencyType
    imported_names: List[str]
    line_number: int
    is_type_only: bool = False


@dataclass
class DependencyNode:
    """Represents a node in the dependency graph."""
    file_path: str
    imports: List[ImportInfo]
    exports: List[str]
    is_entry_point: bool = False
    is_leaf_node: bool = False


class BaseParser:
    """Base class for parsing different file types."""
    
    def __init__(self):
        # Initialize tree-sitter parsers
        self.ts_language = Language(ts_typescript.language_typescript())
        self.tsx_language = Language(ts_typescript.language_tsx())

        self.ts_parser = Parser(self.ts_language)
        self.tsx_parser = Parser(self.tsx_language)
    
    def parse_file(self, file_path: str) -> Optional[DependencyNode]:
        """Parse a file and extract dependency information."""
        raise NotImplementedError("Subclasses must implement parse_file")
    
    def _read_file_content(self, file_path: str) -> str:
        """Read file content with proper encoding handling."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            # Fallback to latin-1 if utf-8 fails
            with open(file_path, 'r', encoding='latin-1') as f:
                return f.read()
    
    def _get_parser_for_file(self, file_path: str) -> Parser:
        """Get the appropriate parser based on file extension."""
        ext = Path(file_path).suffix.lower()
        if ext in ['.tsx', '.jsx']:
            return self.tsx_parser
        else:
            return self.ts_parser
    
    def _resolve_import_path(self, import_path: str, source_file: str) -> Tuple[str, DependencyType]:
        """Resolve import path and determine dependency type."""
        # Remove quotes from import path
        clean_path = import_path.strip('\'"')

        # Determine dependency type
        if clean_path.startswith('.'):
            # Relative import - local dependency
            source_dir = Path(source_file).parent
            resolved_path = (source_dir / clean_path).resolve()
            return str(resolved_path), DependencyType.LOCAL
        elif clean_path.startswith('/'):
            # Absolute path - likely local
            return clean_path, DependencyType.LOCAL
        else:
            # Check if it's a local import without relative path (like 'src/types/User')
            # This happens in some frameworks where imports can be absolute from project root
            if '/' in clean_path and not clean_path.startswith('@'):
                # Try to resolve as project-relative path
                project_root = Path(source_file)
                while project_root.parent != project_root:
                    project_root = project_root.parent
                    if (project_root / clean_path).exists() or any((project_root / clean_path).with_suffix(ext).exists() for ext in ['.ts', '.js', '.vue', '.tsx', '.jsx']):
                        return clean_path, DependencyType.LOCAL

                # Also check relative to source file's project structure
                source_project_root = self._find_project_root(source_file)
                if source_project_root:
                    potential_path = source_project_root / clean_path
                    if potential_path.exists() or any(potential_path.with_suffix(ext).exists() for ext in ['.ts', '.js', '.vue', '.tsx', '.jsx']):
                        return clean_path, DependencyType.LOCAL

            # External dependency (node_modules)
            return clean_path, DependencyType.EXTERNAL

    def _find_project_root(self, file_path: str) -> Optional[Path]:
        """Find the project root by looking for common project files."""
        current = Path(file_path).parent

        while current.parent != current:
            # Look for common project root indicators
            if any((current / indicator).exists() for indicator in ['package.json', 'tsconfig.json', 'vue.config.js', 'src']):
                return current
            current = current.parent

        return None
    
    def _extract_imports_from_tree(self, tree: tree_sitter.Tree, source_code: str, source_file: str) -> List[ImportInfo]:
        """Extract import statements from a tree-sitter parse tree."""
        imports = []

        # Walk the tree to find import statements
        self._walk_tree_for_imports(tree.root_node, source_code, source_file, imports)

        return imports

    def _walk_tree_for_imports(self, node: tree_sitter.Node, source_code: str, source_file: str, imports: List[ImportInfo]):
        """Walk the tree recursively to find import statements."""
        if node.type == "import_statement":
            self._process_import_statement(node, source_code, source_file, imports)
        elif node.type == "call_expression":
            # Check for require() or import() calls
            if node.children and len(node.children) >= 2:
                func_node = node.children[0]
                if func_node.type == "identifier":
                    func_name = source_code[func_node.start_byte:func_node.end_byte]
                    if func_name == "require":
                        self._process_require_statement(node, source_code, source_file, imports)
                elif func_node.type == "import":
                    self._process_dynamic_import(node, source_code, source_file, imports)

        # Recursively process children
        for child in node.children:
            self._walk_tree_for_imports(child, source_code, source_file, imports)
    
    def _process_import_statement(self, node: tree_sitter.Node, source_code: str, source_file: str, imports: List[ImportInfo]):
        """Process ES6 import statements."""
        # Extract source module
        source_node = None
        import_clause_node = None
        
        for child in node.children:
            if child.type == "string":
                source_node = child
            elif child.type == "import_clause":
                import_clause_node = child
        
        if not source_node:
            return
        
        source_text = source_code[source_node.start_byte:source_node.end_byte]
        resolved_path, dep_type = self._resolve_import_path(source_text, source_file)
        
        # Extract imported names and determine import type
        imported_names = []
        import_type = ImportType.DEFAULT
        
        if import_clause_node:
            imported_names, import_type = self._extract_import_names(import_clause_node, source_code)
        
        imports.append(ImportInfo(
            source_file=source_file,
            imported_module=resolved_path,
            import_type=import_type,
            dependency_type=dep_type,
            imported_names=imported_names,
            line_number=node.start_point[0] + 1
        ))
    
    def _process_require_statement(self, node: tree_sitter.Node, source_code: str, source_file: str, imports: List[ImportInfo]):
        """Process CommonJS require statements."""
        # Find the require source
        for child in node.children:
            if child.type == "arguments":
                for arg in child.children:
                    if arg.type == "string":
                        source_text = source_code[arg.start_byte:arg.end_byte]
                        resolved_path, dep_type = self._resolve_import_path(source_text, source_file)
                        
                        imports.append(ImportInfo(
                            source_file=source_file,
                            imported_module=resolved_path,
                            import_type=ImportType.REQUIRE,
                            dependency_type=dep_type,
                            imported_names=[],
                            line_number=node.start_point[0] + 1
                        ))
                        break
    
    def _process_dynamic_import(self, node: tree_sitter.Node, source_code: str, source_file: str, imports: List[ImportInfo]):
        """Process dynamic import() statements."""
        # Find the import source
        for child in node.children:
            if child.type == "arguments":
                for arg in child.children:
                    if arg.type == "string":
                        source_text = source_code[arg.start_byte:arg.end_byte]
                        resolved_path, dep_type = self._resolve_import_path(source_text, source_file)
                        
                        imports.append(ImportInfo(
                            source_file=source_file,
                            imported_module=resolved_path,
                            import_type=ImportType.DYNAMIC,
                            dependency_type=dep_type,
                            imported_names=[],
                            line_number=node.start_point[0] + 1
                        ))
                        break
    
    def _extract_import_names(self, import_clause_node: tree_sitter.Node, source_code: str) -> Tuple[List[str], ImportType]:
        """Extract imported names from import clause."""
        imported_names = []
        import_type = ImportType.DEFAULT
        
        for child in import_clause_node.children:
            if child.type == "identifier":
                # Default import
                name = source_code[child.start_byte:child.end_byte]
                imported_names.append(name)
                import_type = ImportType.DEFAULT
            elif child.type == "named_imports":
                # Named imports
                import_type = ImportType.NAMED
                for named_child in child.children:
                    if named_child.type == "import_specifier":
                        for spec_child in named_child.children:
                            if spec_child.type == "identifier":
                                name = source_code[spec_child.start_byte:spec_child.end_byte]
                                imported_names.append(name)
            elif child.type == "namespace_import":
                # Namespace import (import * as name)
                import_type = ImportType.NAMESPACE
                for ns_child in child.children:
                    if ns_child.type == "identifier":
                        name = source_code[ns_child.start_byte:ns_child.end_byte]
                        imported_names.append(name)
        
        return imported_names, import_type
