{"project_root": "/home/<USER>/diff_analyzer/test_project", "statistics": {"total_files": 13, "total_dependencies": 0, "entry_points": 13, "leaf_nodes": 13, "circular_dependencies": 0, "external_packages": 3, "avg_dependencies_per_file": 0.0, "avg_dependents_per_file": 0.0, "max_dependencies": 0, "max_dependents": 0, "file_types": {".ts": 6, ".vue": 7}, "most_imported_files": [["/home/<USER>/diff_analyzer/test_project/src/main.ts", 0], ["/home/<USER>/diff_analyzer/test_project/src/App.vue", 0], ["/home/<USER>/diff_analyzer/test_project/src/store/index.ts", 0], ["/home/<USER>/diff_analyzer/test_project/src/components/SocialLinks.vue", 0], ["/home/<USER>/diff_analyzer/test_project/src/components/ContactInfo.vue", 0]], "most_dependent_files": [["/home/<USER>/diff_analyzer/test_project/src/main.ts", 0], ["/home/<USER>/diff_analyzer/test_project/src/App.vue", 0], ["/home/<USER>/diff_analyzer/test_project/src/store/index.ts", 0], ["/home/<USER>/diff_analyzer/test_project/src/components/SocialLinks.vue", 0], ["/home/<USER>/diff_analyzer/test_project/src/components/ContactInfo.vue", 0]]}, "files": {"src/main.ts": {"path": "src/main.ts", "is_entry_point": true, "is_leaf_node": false, "dependencies": [], "dependents": []}, "src/App.vue": {"path": "src/App.vue", "is_entry_point": true, "is_leaf_node": false, "dependencies": [], "dependents": []}, "src/store/index.ts": {"path": "src/store/index.ts", "is_entry_point": true, "is_leaf_node": false, "dependencies": [], "dependents": []}, "src/components/SocialLinks.vue": {"path": "src/components/SocialLinks.vue", "is_entry_point": false, "is_leaf_node": false, "dependencies": [], "dependents": []}, "src/components/ContactInfo.vue": {"path": "src/components/ContactInfo.vue", "is_entry_point": false, "is_leaf_node": false, "dependencies": [], "dependents": []}, "src/components/UserAvatar.vue": {"path": "src/components/UserAvatar.vue", "is_entry_point": false, "is_leaf_node": false, "dependencies": [], "dependents": []}, "src/components/UserProfile.vue": {"path": "src/components/UserProfile.vue", "is_entry_point": false, "is_leaf_node": false, "dependencies": [], "dependents": []}, "src/utils/userService.ts": {"path": "src/utils/userService.ts", "is_entry_point": false, "is_leaf_node": false, "dependencies": [], "dependents": []}, "src/utils/apiClient.ts": {"path": "src/utils/apiClient.ts", "is_entry_point": false, "is_leaf_node": true, "dependencies": [], "dependents": []}, "src/router/index.ts": {"path": "src/router/index.ts", "is_entry_point": true, "is_leaf_node": false, "dependencies": [], "dependents": []}, "src/views/About.vue": {"path": "src/views/About.vue", "is_entry_point": false, "is_leaf_node": false, "dependencies": [], "dependents": []}, "src/views/Home.vue": {"path": "src/views/Home.vue", "is_entry_point": false, "is_leaf_node": false, "dependencies": [], "dependents": []}, "src/types/User.ts": {"path": "src/types/User.ts", "is_entry_point": false, "is_leaf_node": true, "dependencies": [], "dependents": []}}, "external_dependencies": {"vue": ["/home/<USER>/diff_analyzer/test_project/src/main.ts", "/home/<USER>/diff_analyzer/test_project/src/App.vue", "/home/<USER>/diff_analyzer/test_project/src/components/SocialLinks.vue", "/home/<USER>/diff_analyzer/test_project/src/components/ContactInfo.vue", "/home/<USER>/diff_analyzer/test_project/src/components/UserAvatar.vue", "/home/<USER>/diff_analyzer/test_project/src/components/UserProfile.vue", "/home/<USER>/diff_analyzer/test_project/src/views/About.vue", "/home/<USER>/diff_analyzer/test_project/src/views/Home.vue"], "vuex": ["/home/<USER>/diff_analyzer/test_project/src/store/index.ts"], "vue-router": ["/home/<USER>/diff_analyzer/test_project/src/router/index.ts"]}, "circular_dependencies": [], "entry_points": ["src/main.ts", "src/App.vue", "src/store/index.ts", "src/components/SocialLinks.vue", "src/components/ContactInfo.vue", "src/components/UserAvatar.vue", "src/components/UserProfile.vue", "src/utils/userService.ts", "src/utils/apiClient.ts", "src/router/index.ts", "src/views/About.vue", "src/views/Home.vue", "src/types/User.ts"], "leaf_nodes": ["src/main.ts", "src/App.vue", "src/store/index.ts", "src/components/SocialLinks.vue", "src/components/ContactInfo.vue", "src/components/UserAvatar.vue", "src/components/UserProfile.vue", "src/utils/userService.ts", "src/utils/apiClient.ts", "src/router/index.ts", "src/views/About.vue", "src/views/Home.vue", "src/types/User.ts"]}