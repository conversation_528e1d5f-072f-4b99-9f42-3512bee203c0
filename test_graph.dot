digraph DependencyGraph {
  rankdir=TB;
  node [shape=box, style=filled];

  // Node styles
  node [fillcolor=lightblue];
  // Entry points
  node [fillcolor=lightgreen];
  "src/main.ts";
  "src/App.vue";
  "src/store/index.ts";
  "src/router/index.ts";
  // Leaf nodes
  node [fillcolor=lightyellow];
  "src/components/UserAvatar.vue";
  "src/utils/apiClient.ts";
  "src/types/User.ts";
  // Regular nodes
  node [fillcolor=lightblue];
  "src/components/SocialLinks.vue";
  "src/components/ContactInfo.vue";
  "src/components/UserProfile.vue";
  "src/utils/userService.ts";
  "src/views/About.vue";
  "src/views/Home.vue";

  // Dependencies
  "src/main.ts" -> "src/App.vue" [label="default"];
  "src/main.ts" -> "src/router/index.ts" [label="default"];
  "src/main.ts" -> "src/store/index.ts" [label="named"];
  "src/App.vue" -> "src/components/UserProfile.vue" [label="default"];
  "src/App.vue" -> "src/store/index.ts" [label="named"];
  "src/store/index.ts" -> "src/types/User.ts" [label="named"];
  "src/store/index.ts" -> "src/utils/userService.ts" [label="named"];
  "src/components/SocialLinks.vue" -> "src/types/User.ts" [label="named"];
  "src/components/ContactInfo.vue" -> "src/components/SocialLinks.vue" [label="default"];
  "src/components/ContactInfo.vue" -> "src/types/User.ts" [label="named"];
  "src/components/UserProfile.vue" -> "src/components/UserAvatar.vue" [label="default"];
  "src/components/UserProfile.vue" -> "src/components/ContactInfo.vue" [label="default"];
  "src/components/UserProfile.vue" -> "src/types/User.ts" [label="named"];
  "src/utils/userService.ts" -> "src/types/User.ts" [label="named"];
  "src/utils/userService.ts" -> "src/utils/apiClient.ts" [label="named"];
  "src/router/index.ts" -> "src/views/Home.vue" [label="default"];
  "src/router/index.ts" -> "src/views/About.vue" [label="dynamic"];
  "src/views/About.vue" -> "src/components/ContactInfo.vue" [label="default"];
  "src/views/About.vue" -> "src/types/User.ts" [label="named"];
  "src/views/Home.vue" -> "src/components/UserProfile.vue" [label="default"];
  "src/views/Home.vue" -> "src/types/User.ts" [label="named"];
  "src/views/Home.vue" -> "src/utils/userService.ts" [label="named"];
}