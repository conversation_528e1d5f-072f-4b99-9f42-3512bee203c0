<template>
  <div class="about">
    <h1>About This App</h1>
    <p>This is a sample Vue.js application for testing dependency analysis.</p>
    <ContactInfo :contact="appContact" />
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import ContactInfo from '../components/ContactInfo.vue'
import { Contact } from '../types/User'

export default defineComponent({
  name: 'About',
  components: {
    ContactInfo
  },
  setup() {
    const appContact: Contact = {
      phone: '******-0123',
      address: '123 Main St, Anytown, USA',
      social: [
        { platform: 'GitHub', url: 'https://github.com/example' },
        { platform: 'Twitter', url: 'https://twitter.com/example' }
      ]
    }

    return {
      appContact
    }
  }
})
</script>

<style scoped>
.about {
  padding: 20px;
}
</style>
