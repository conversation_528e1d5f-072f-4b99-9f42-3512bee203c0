import { User } from '../types/User'
import { apiClient } from './apiClient'

export const userService = {
  async getUser(id: number): Promise<User> {
    const response = await apiClient.get(`/users/${id}`)
    return response.data
  },

  async updateUser(id: number, userData: Partial<User>): Promise<User> {
    const response = await apiClient.put(`/users/${id}`, userData)
    return response.data
  },

  async deleteUser(id: number): Promise<void> {
    await apiClient.delete(`/users/${id}`)
  }
}
