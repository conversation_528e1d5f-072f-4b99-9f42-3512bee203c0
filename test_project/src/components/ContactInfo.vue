<template>
  <div class="contact-info">
    <h3>Contact Information</h3>
    <p v-if="contact.phone">Phone: {{ contact.phone }}</p>
    <p v-if="contact.address">Address: {{ contact.address }}</p>
    <SocialLinks :links="contact.social" />
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue'
import SocialLinks from './SocialLinks.vue'
import { Contact } from '../types/User'

export default defineComponent({
  name: 'ContactInfo',
  components: {
    SocialLinks
  },
  props: {
    contact: {
      type: Object as PropType<Contact>,
      required: true
    }
  }
})
</script>

<style scoped>
.contact-info {
  margin-top: 16px;
}
</style>
