<template>
  <div id="app">
    <nav>
      <router-link to="/">Home</router-link>
      <router-link to="/about">About</router-link>
    </nav>
    <router-view />
    <UserProfile :user="currentUser" />
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import UserProfile from './components/UserProfile.vue'
import { useStore } from './store'

export default defineComponent({
  name: 'App',
  components: {
    UserProfile
  },
  setup() {
    const store = useStore()
    
    return {
      currentUser: store.state.user
    }
  }
})
</script>

<style scoped>
nav {
  padding: 30px;
}

nav a {
  font-weight: bold;
  color: #2c3e50;
}

nav a.router-link-exact-active {
  color: #42b983;
}
</style>
