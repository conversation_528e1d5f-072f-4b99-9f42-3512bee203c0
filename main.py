#!/usr/bin/env python3
"""
Vue.js and React Dependency Analyzer

A comprehensive tool for analyzing dependency relationships in Vue.js and React projects
using tree-sitter-typescript for accurate parsing.

Usage:
    python main.py [PROJECT_PATH] [OPTIONS]

Examples:
    python main.py ./my-vue-project --format json --output deps.json
    python main.py ./my-react-app --format mermaid --show-external
    python main.py . --format dot --exclude node_modules dist
"""

import sys
from pathlib import Path
from typing import List, Optional

import click

from dependency_graph import DependencyGraph
from output_formatters import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, D<PERSON><PERSON>ormatter, MermaidFormatter, TextTreeFormatter


@click.command()
@click.argument('project_path', type=click.Path(exists=True, file_okay=False, dir_okay=True), default='.')
@click.option('--format', '-f',
              type=click.Choice(['json', 'dot', 'mermaid', 'text'], case_sensitive=False),
              default='text',
              help='Output format for the dependency analysis')
@click.option('--output', '-o',
              type=click.Path(),
              help='Output file path (default: stdout)')
@click.option('--exclude', '-e',
              multiple=True,
              default=['node_modules', '.git', 'dist', 'build', '.next', '.nuxt'],
              help='Patterns to exclude from analysis (can be used multiple times)')
@click.option('--include-external/--no-external',
              default=False,
              help='Include external dependencies in output')
@click.option('--show-details/--no-details',
              default=False,
              help='Show detailed information in output')
@click.option('--find-cycles/--no-cycles',
              default=True,
              help='Find and report circular dependencies')
@click.option('--stats-only',
              is_flag=True,
              help='Show only statistics, no detailed dependency information')
@click.option('--mermaid-type',
              type=click.Choice(['flowchart', 'graph'], case_sensitive=False),
              default='flowchart',
              help='Type of Mermaid diagram to generate')
@click.option('--verbose', '-v',
              is_flag=True,
              help='Enable verbose output')
def main(project_path: str, format: str, output: Optional[str], exclude: List[str],
         include_external: bool, show_details: bool, find_cycles: bool,
         stats_only: bool, mermaid_type: str, verbose: bool):
    """
    Analyze dependency relationships in Vue.js and React projects.

    PROJECT_PATH: Path to the project directory to analyze (default: current directory)
    """

    # Convert to absolute path
    project_path_obj = Path(project_path).resolve()

    if verbose:
        click.echo(f"Analyzing project: {project_path_obj}")
        click.echo(f"Output format: {format}")
        click.echo(f"Exclude patterns: {list(exclude)}")

    # Initialize dependency graph
    try:
        dependency_graph = DependencyGraph(str(project_path_obj))

        if verbose:
            click.echo("Building dependency graph...")

        # Analyze the project
        dependency_graph.analyze_project(exclude_patterns=list(exclude))

        if verbose:
            stats = dependency_graph.get_statistics()
            click.echo(f"Found {stats['total_files']} files with {stats['total_dependencies']} dependencies")

        # Generate output based on format
        if stats_only:
            result = _generate_stats_only(dependency_graph)
        else:
            result = _generate_output(dependency_graph, format, include_external,
                                    show_details, mermaid_type)

        # Output result
        if output:
            with open(output, 'w', encoding='utf-8') as f:
                f.write(result)
            click.echo(f"Output written to: {output}")
        else:
            click.echo(result)

        # Show warnings for circular dependencies
        if find_cycles:
            cycles = dependency_graph.find_circular_dependencies()
            if cycles:
                click.echo(f"\n⚠️  Found {len(cycles)} circular dependencies!", err=True)
                if verbose:
                    for i, cycle in enumerate(cycles, 1):
                        cycle_str = " → ".join([Path(f).name for f in cycle])
                        click.echo(f"  Cycle {i}: {cycle_str}", err=True)

    except Exception as e:
        click.echo(f"Error analyzing project: {e}", err=True)
        if verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def _generate_output(dependency_graph: DependencyGraph, format: str,
                    include_external: bool, show_details: bool,
                    mermaid_type: str) -> str:
    """Generate output in the specified format."""

    if format.lower() == 'json':
        formatter = JSONFormatter(dependency_graph)
        return formatter.format(include_details=show_details)

    elif format.lower() == 'dot':
        formatter = DOTFormatter(dependency_graph)
        return formatter.format(include_external=include_external)

    elif format.lower() == 'mermaid':
        formatter = MermaidFormatter(dependency_graph)
        return formatter.format(diagram_type=mermaid_type)

    elif format.lower() == 'text':
        formatter = TextTreeFormatter(dependency_graph)
        return formatter.format(show_details=show_details)

    else:
        raise ValueError(f"Unsupported format: {format}")


def _generate_stats_only(dependency_graph: DependencyGraph) -> str:
    """Generate statistics-only output."""
    stats = dependency_graph.get_statistics()

    lines = [
        "Project Dependency Statistics",
        "=" * 40,
        "",
        f"📁 Total Files: {stats['total_files']}",
        f"🔗 Total Dependencies: {stats['total_dependencies']}",
        f"🚀 Entry Points: {stats['entry_points']}",
        f"🍃 Leaf Nodes: {stats['leaf_nodes']}",
        f"🔄 Circular Dependencies: {stats['circular_dependencies']}",
        f"📦 External Packages: {stats['external_packages']}",
        "",
        f"📊 Average Dependencies per File: {stats['avg_dependencies_per_file']:.2f}",
        f"📊 Average Dependents per File: {stats['avg_dependents_per_file']:.2f}",
        f"📊 Max Dependencies: {stats['max_dependencies']}",
        f"📊 Max Dependents: {stats['max_dependents']}",
        "",
        "File Type Distribution:",
    ]

    for file_type, count in stats['file_types'].items():
        lines.append(f"  {file_type}: {count}")

    lines.extend([
        "",
        "Most Imported Files:",
    ])

    for file_path, count in stats['most_imported_files']:
        rel_path = str(Path(file_path).relative_to(dependency_graph.project_root))
        lines.append(f"  {rel_path}: {count} imports")

    lines.extend([
        "",
        "Most Dependent Files:",
    ])

    for file_path, count in stats['most_dependent_files']:
        rel_path = str(Path(file_path).relative_to(dependency_graph.project_root))
        lines.append(f"  {rel_path}: {count} dependencies")

    return "\n".join(lines)


@click.group()
def cli():
    """Vue.js and React Dependency Analyzer CLI"""
    pass


@cli.command()
@click.argument('project_path', type=click.Path(exists=True, file_okay=False, dir_okay=True))
@click.argument('source_file', type=str)
@click.argument('target_file', type=str)
def path(project_path: str, source_file: str, target_file: str):
    """Find the shortest dependency path between two files."""

    dependency_graph = DependencyGraph(project_path)
    dependency_graph.analyze_project()

    # Convert relative paths to absolute
    project_root = Path(project_path).resolve()
    source_path = str(project_root / source_file)
    target_path = str(project_root / target_file)

    shortest_path = dependency_graph.get_shortest_path(source_path, target_path)

    if shortest_path:
        click.echo("Dependency path found:")
        for i, file_path in enumerate(shortest_path):
            rel_path = str(Path(file_path).relative_to(project_root))
            prefix = "  " + ("└─ " if i == len(shortest_path) - 1 else "├─ ")
            click.echo(f"{prefix}{rel_path}")
    else:
        click.echo("No dependency path found between the specified files.")


@cli.command()
@click.argument('project_path', type=click.Path(exists=True, file_okay=False, dir_okay=True))
@click.argument('file_path', type=str)
def analyze_file(project_path: str, file_path: str):
    """Analyze a specific file and show its dependencies and dependents."""

    dependency_graph = DependencyGraph(project_path)
    dependency_graph.analyze_project()

    # Convert relative path to absolute
    project_root = Path(project_path).resolve()
    target_file = str(project_root / file_path)

    if target_file not in dependency_graph.nodes:
        click.echo(f"File not found in dependency graph: {file_path}")
        return

    node = dependency_graph.nodes[target_file]
    dependencies = dependency_graph.get_dependencies(target_file)
    dependents = dependency_graph.get_dependents(target_file)

    click.echo(f"Analysis for: {file_path}")
    click.echo("=" * 50)
    click.echo(f"Entry Point: {'Yes' if node.is_entry_point else 'No'}")
    click.echo(f"Leaf Node: {'Yes' if node.is_leaf_node else 'No'}")
    click.echo(f"Dependencies: {len(dependencies)}")
    click.echo(f"Dependents: {len(dependents)}")
    click.echo()

    if dependencies:
        click.echo("Dependencies:")
        for dep in dependencies:
            rel_path = str(Path(dep).relative_to(project_root))
            click.echo(f"  ├─ {rel_path}")

    if dependents:
        click.echo("Dependents:")
        for dep in dependents:
            rel_path = str(Path(dep).relative_to(project_root))
            click.echo(f"  ├─ {rel_path}")


if __name__ == '__main__':
    # Check if it's a subcommand
    if len(sys.argv) > 1 and sys.argv[1] in ['path', 'analyze-file']:
        cli()
    else:
        main()