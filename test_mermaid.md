flowchart TD
  N0["src/main.ts"]
  N0 --> N0
  N1["src/App.vue"]
  N1 --> N1
  N2["src/store/index.ts"]
  N2 --> N2
  N3["src/components/SocialLinks.vue"]
  N4["src/components/ContactInfo.vue"]
  N5("src/components/UserAvatar.vue")
  N6["src/components/UserProfile.vue"]
  N7["src/utils/userService.ts"]
  N8("src/utils/apiClient.ts")
  N9["src/router/index.ts"]
  N9 --> N9
  N10["src/views/About.vue"]
  N11["src/views/Home.vue"]
  N12("src/types/User.ts")

  N0 --> N1
  N0 --> N9
  N0 --> N2
  N1 --> N6
  N1 --> N2
  N2 --> N12
  N2 --> N7
  N3 --> N12
  N4 --> N3
  N4 --> N12
  N6 --> N5
  N6 --> N4
  N6 --> N12
  N7 --> N12
  N7 --> N8
  N9 --> N11
  N9 --> N10
  N10 --> N4
  N10 --> N12
  N11 --> N6
  N11 --> N12
  N11 --> N7

  %% Styling
  classDef entryPoint fill:#90EE90
  classDef leafNode fill:#FFE4B5
  classDef regularNode fill:#ADD8E6
  class N0 entryPoint
  class N1 entryPoint
  class N2 entryPoint
  class N3 regularNode
  class N4 regularNode
  class N5 leafNode
  class N6 regularNode
  class N7 regularNode
  class N8 leafNode
  class N9 entryPoint
  class N10 regularNode
  class N11 regularNode
  class N12 leafNode